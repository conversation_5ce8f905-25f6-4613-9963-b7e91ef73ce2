<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Attendance Tracker Template</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .download-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #e8f4fd;
            border-radius: 5px;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin: 10px;
        }
        .download-btn:hover {
            background-color: #2980b9;
        }
        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }
        .preview-table th, .preview-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .preview-table th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        .preview-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .feature-card h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Complete Excel Attendance Tracker</h1>
        
        <div class="download-section">
            <h2>Download Your Attendance Tracker</h2>
            <p>Click the button below to download the Excel file with pre-built formulas and formatting:</p>
            <a href="#" class="download-btn" onclick="downloadExcel()">📥 Download Excel File</a>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>🎯 Key Features</h3>
                <ul>
                    <li>Employee information management</li>
                    <li>Monthly attendance tracking</li>
                    <li>Automatic calculations</li>
                    <li>Professional formatting</li>
                    <li>Summary statistics</li>
                    <li>Color-coded status indicators</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>📈 Automatic Calculations</h3>
                <ul>
                    <li>Total working days</li>
                    <li>Days present/absent</li>
                    <li>Attendance percentage</li>
                    <li>Late arrivals count</li>
                    <li>Early departures count</li>
                    <li>Overtime hours</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>🎨 Professional Design</h3>
                <ul>
                    <li>Clean, organized layout</li>
                    <li>Color-coded attendance status</li>
                    <li>Proper cell alignment</li>
                    <li>Conditional formatting</li>
                    <li>Print-friendly format</li>
                    <li>Easy data entry</li>
                </ul>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 How to Use</h3>
            <ol>
                <li><strong>Download the Excel file</strong> using the button above</li>
                <li><strong>Enter employee details</strong> in the Employee Information section</li>
                <li><strong>Mark attendance daily</strong> using the provided codes:
                    <ul>
                        <li><strong>P</strong> = Present</li>
                        <li><strong>A</strong> = Absent</li>
                        <li><strong>L</strong> = Late</li>
                        <li><strong>H</strong> = Holiday</li>
                        <li><strong>S</strong> = Sick Leave</li>
                        <li><strong>V</strong> = Vacation</li>
                    </ul>
                </li>
                <li><strong>Review automatic calculations</strong> in the summary section</li>
                <li><strong>Customize</strong> as needed for your organization</li>
            </ol>
        </div>

        <h2>📋 Preview of Attendance Tracker Layout</h2>
        <table class="preview-table">
            <tr>
                <th rowspan="2">Employee ID</th>
                <th rowspan="2">Employee Name</th>
                <th rowspan="2">Department</th>
                <th colspan="31">Days of Month</th>
                <th rowspan="2">Total Present</th>
                <th rowspan="2">Total Absent</th>
                <th rowspan="2">Attendance %</th>
            </tr>
            <tr>
                <th>1</th><th>2</th><th>3</th><th>4</th><th>5</th><th>6</th><th>7</th><th>8</th><th>9</th><th>10</th>
                <th>11</th><th>12</th><th>13</th><th>14</th><th>15</th><th>16</th><th>17</th><th>18</th><th>19</th><th>20</th>
                <th>21</th><th>22</th><th>23</th><th>24</th><th>25</th><th>26</th><th>27</th><th>28</th><th>29</th><th>30</th><th>31</th>
            </tr>
            <tr>
                <td>EMP001</td>
                <td>John Smith</td>
                <td>IT</td>
                <td>P</td><td>P</td><td>A</td><td>P</td><td>P</td><td>H</td><td>H</td><td>P</td><td>P</td><td>L</td>
                <td>P</td><td>P</td><td>P</td><td>H</td><td>P</td><td>P</td><td>P</td><td>P</td><td>P</td><td>P</td>
                <td>P</td><td>P</td><td>S</td><td>P</td><td>P</td><td>P</td><td>P</td><td>26</td><td>3</td><td>89.7%</td>
            </tr>
            <tr>
                <td>EMP002</td>
                <td>Jane Doe</td>
                <td>HR</td>
                <td>P</td><td>P</td><td>P</td><td>P</td><td>P</td><td>H</td><td>H</td><td>P</td><td>P</td><td>P</td>
                <td>P</td><td>P</td><td>P</td><td>H</td><td>P</td><td>P</td><td>P</td><td>P</td><td>P</td><td>P</td>
                <td>P</td><td>P</td><td>P</td><td>P</td><td>P</td><td>P</td><td>P</td><td>28</td><td>1</td><td>96.6%</td>
            </tr>
        </table>
    </div>

    <script>
        function downloadExcel() {
            // Create Excel content with proper formatting
            const excelContent = createExcelContent();
            
            // Create and download the file
            const blob = new Blob([excelContent], { 
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
            });
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'Attendance_Tracker_Template.xlsx';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function createExcelContent() {
            // This is a simplified version - in a real implementation, 
            // you would use a library like SheetJS to create proper Excel files
            return `Employee ID,Employee Name,Department,Position,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,Total Present,Total Absent,Attendance %,Late Count,Early Leave,Overtime Hours
EMP001,John Smith,IT,Developer,P,P,A,P,P,H,H,P,P,L,P,P,P,H,P,P,P,P,P,P,P,P,S,P,P,P,P,=COUNTIF(E2:AI2,"P"),=COUNTIF(E2:AI2,"A"),=AK2/(AK2+AL2)*100,=COUNTIF(E2:AI2,"L"),0,0
EMP002,Jane Doe,HR,Manager,P,P,P,P,P,H,H,P,P,P,P,P,P,H,P,P,P,P,P,P,P,P,P,P,P,P,P,=COUNTIF(E3:AI3,"P"),=COUNTIF(E3:AI3,"A"),=AK3/(AK3+AL3)*100,=COUNTIF(E3:AI3,"L"),0,0`;
        }
    </script>
</body>
</html>
