<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Employee Attendance Tracker</Title>
  <Author>Attendance System</Author>
  <Created>2025-07-02T00:00:00Z</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>18000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Center" ss:Horizontal="Center"/>
   <Borders/>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="10"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="Header">
   <Alignment ss:Vertical="Center" ss:Horizontal="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="2"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="2"/>
   </Borders>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="10" ss:Bold="1" ss:Color="#FFFFFF"/>
   <Interior ss:Color="#4472C4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="Title">
   <Alignment ss:Vertical="Center" ss:Horizontal="Center"/>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="16" ss:Bold="1" ss:Color="#2F5597"/>
  </Style>
  <Style ss:ID="DataCell">
   <Alignment ss:Vertical="Center" ss:Horizontal="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="9"/>
  </Style>
  <Style ss:ID="Present">
   <Alignment ss:Vertical="Center" ss:Horizontal="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="9" ss:Bold="1"/>
   <Interior ss:Color="#C6EFCE" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="Absent">
   <Alignment ss:Vertical="Center" ss:Horizontal="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Arial" x:Family="Swiss" ss:Size="9" ss:Bold="1"/>
   <Interior ss:Color="#FFC7CE" ss:Pattern="Solid"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Attendance Tracker">
  <Table ss:ExpandedColumnCount="40" ss:ExpandedRowCount="50" x:FullColumns="1" x:FullRows="1">
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="120"/>
   <Column ss:AutoFitWidth="0" ss:Width="100"/>
   <Column ss:AutoFitWidth="0" ss:Width="100"/>
   <Column ss:AutoFitWidth="0" ss:Width="25" ss:Span="30"/>
   <Column ss:Index="36" ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="90"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Row ss:Height="25">
    <Cell ss:MergeAcross="39" ss:StyleID="Title"><Data ss:Type="String">EMPLOYEE ATTENDANCE TRACKER</Data></Cell>
   </Row>
   <Row ss:Height="20">
    <Cell ss:MergeAcross="39" ss:StyleID="Title"><Data ss:Type="String">MONTH: [ENTER MONTH/YEAR]</Data></Cell>
   </Row>
   <Row ss:Height="15"/>
   <Row ss:Height="20">
    <Cell ss:StyleID="Header"><Data ss:Type="String">Employee ID</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Employee Name</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Department</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Position</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">1</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">2</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">3</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">4</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">5</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">6</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">7</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">8</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">9</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">10</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">11</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">12</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">13</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">14</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">15</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">16</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">17</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">18</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">19</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">20</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">21</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">22</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">23</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">24</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">25</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">26</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">27</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">28</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">29</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">30</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="Number">31</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Total Present</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Total Absent</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Attendance %</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Late Count</Data></Cell>
    <Cell ss:StyleID="Header"><Data ss:Type="String">Early Leave</Data></Cell>
   </Row>
   <Row ss:Height="18">
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">EMP001</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">John Smith</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">IT</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">Developer</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Absent"><Data ss:Type="String">A</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">H</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">H</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">L</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">H</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="String">S</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="Present"><Data ss:Type="String">P</Data></Cell>
    <Cell ss:StyleID="DataCell" ss:Formula="=COUNTIF(E5:AI5,&quot;P&quot;)"><Data ss:Type="Number">26</Data></Cell>
    <Cell ss:StyleID="DataCell" ss:Formula="=COUNTIF(E5:AI5,&quot;A&quot;)"><Data ss:Type="Number">1</Data></Cell>
    <Cell ss:StyleID="DataCell" ss:Formula="=ROUND(AJ5/(AJ5+AK5)*100,1)&amp;&quot;%&quot;"><Data ss:Type="String">96.3%</Data></Cell>
    <Cell ss:StyleID="DataCell" ss:Formula="=COUNTIF(E5:AI5,&quot;L&quot;)"><Data ss:Type="Number">1</Data></Cell>
    <Cell ss:StyleID="DataCell"><Data ss:Type="Number">0</Data></Cell>
   </Row>
  </Table>
 </Worksheet>
</Workbook>
